import json
import logging

import azure.functions as func

app = func.FunctionApp(http_auth_level=func.AuthLevel.FUNCTION)

_LOCATION_PROPERTY_NAME = "location"
_DATE_PROPERTY_NAME = "weather_date_string"

class ToolProperty:
    def __init__(self, property_name: str, property_type: str, description: str):
        self.propertyName = property_name
        self.propertyType = property_type
        self.description = description

    def to_dict(self):
        return {
            "propertyName": self.propertyName,
            "propertyType": self.propertyType,
            "description": self.description,
        }


tool_properties_get_location_date_object = [
    ToolProperty(_LOCATION_PROPERTY_NAME, "string", "The name of the location."), 
    ToolProperty(_DATE_PROPERTY_NAME, "string", "The date for which the weather is requested from the original query, no need to translate to DateTime format if user doesn't make it to a DateTime format.")
]

tool_properties_get_location_date_json = json.dumps([prop.to_dict() for prop in tool_properties_get_location_date_object])

@app.generic_trigger(
    arg_name="context",
    type="mcpToolTrigger",
    toolName="weather_mcp",
    description="This function returns the weather info from the server.",
    toolProperties=tool_properties_get_location_date_json,
)
def weather_mcp(context) -> None:
    """
    A function to return the weather info of which date from the server.

    Args:
        context: The trigger context (not used in this function).

    Returns:
        str: the weather.
    """
    content = json.loads(context)
    location_from_args = content["arguments"][_LOCATION_PROPERTY_NAME]
    date_from_args = content["arguments"][_DATE_PROPERTY_NAME]

    logging.info("Weather MCP function triggered.")
    logging.info(f"Context: {context}")
    logging.info(f"Location from arguments: {location_from_args}")
    logging.info(f"Date from arguments: {date_from_args}")

    return f"The weather in {location_from_args} is sunny with a temperature of 25°C."
