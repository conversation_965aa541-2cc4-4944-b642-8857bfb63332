import json
import logging
import os
from typing import Dict, Any

import azure.functions as func

app = func.FunctionApp(http_auth_level=func.AuthLevel.FUNCTION)

_USER_QUERY_PROPERTY_NAME = "user_query"
_ANALYSIS_TYPE_PROPERTY_NAME = "analysis_type"

class ToolProperty:
    def __init__(self, property_name: str, property_type: str, description: str):
        self.propertyName = property_name
        self.propertyType = property_type
        self.description = description

    def to_dict(self):
        return {
            "propertyName": self.propertyName,
            "propertyType": self.propertyType,
            "description": self.description,
        }


# Tool properties for topic analysis
tool_properties_topic_analyzer = [
    ToolProperty(_USER_QUERY_PROPERTY_NAME, "string", "The user query to analyze for topic extraction."), 
    ToolProperty(_ANALYSIS_TYPE_PROPERTY_NAME, "string", "Type of analysis to perform: 'topic', 'sentiment', or 'intent'.")
]

tool_properties_topic_analyzer_json = json.dumps([prop.to_dict() for prop in tool_properties_topic_analyzer])


def pseudo_azure_openai_call(user_query: str, analysis_type: str) -> Dict[str, Any]:
    """
    Pseudo-function simulating Azure OpenAI API call for topic analysis.
    
    In a real implementation, this would make an actual call to Azure OpenAI service.
    
    Args:
        user_query: The user's input query
        analysis_type: Type of analysis to perform
        
    Returns:
        Dict containing the analysis results
    """
    
    # Simulate different types of analysis based on analysis_type
    if analysis_type.lower() == "topic":
        # Topic extraction simulation
        topics = []
        query_lower = user_query.lower()
        
        # Simple keyword-based topic detection (in real implementation, use Azure OpenAI)
        if any(word in query_lower for word in ["weather", "temperature", "rain", "sunny", "cloudy"]):
            topics.append("Weather")
        if any(word in query_lower for word in ["food", "restaurant", "eat", "meal", "cooking"]):
            topics.append("Food & Dining")
        if any(word in query_lower for word in ["travel", "trip", "vacation", "flight", "hotel"]):
            topics.append("Travel")
        if any(word in query_lower for word in ["technology", "computer", "software", "app", "tech"]):
            topics.append("Technology")
        if any(word in query_lower for word in ["health", "medical", "doctor", "medicine", "fitness"]):
            topics.append("Health")
        
        if not topics:
            topics.append("General")
            
        return {
            "analysis_type": "topic_extraction",
            "topics": topics,
            "confidence": 0.85,
            "primary_topic": topics[0] if topics else "General"
        }
    
    elif analysis_type.lower() == "sentiment":
        # Sentiment analysis simulation
        query_lower = user_query.lower()
        
        positive_words = ["good", "great", "excellent", "amazing", "wonderful", "love", "like", "happy"]
        negative_words = ["bad", "terrible", "awful", "hate", "dislike", "sad", "angry", "frustrated"]
        
        positive_count = sum(1 for word in positive_words if word in query_lower)
        negative_count = sum(1 for word in negative_words if word in query_lower)
        
        if positive_count > negative_count:
            sentiment = "positive"
            confidence = min(0.9, 0.6 + (positive_count * 0.1))
        elif negative_count > positive_count:
            sentiment = "negative"
            confidence = min(0.9, 0.6 + (negative_count * 0.1))
        else:
            sentiment = "neutral"
            confidence = 0.7
            
        return {
            "analysis_type": "sentiment_analysis",
            "sentiment": sentiment,
            "confidence": confidence,
            "positive_indicators": positive_count,
            "negative_indicators": negative_count
        }
    
    elif analysis_type.lower() == "intent":
        # Intent classification simulation
        query_lower = user_query.lower()
        
        if any(word in query_lower for word in ["what", "how", "why", "when", "where", "who"]):
            intent = "information_seeking"
        elif any(word in query_lower for word in ["book", "reserve", "schedule", "buy", "purchase"]):
            intent = "transactional"
        elif any(word in query_lower for word in ["help", "support", "problem", "issue", "error"]):
            intent = "support"
        elif any(word in query_lower for word in ["recommend", "suggest", "advice", "opinion"]):
            intent = "recommendation"
        else:
            intent = "general_conversation"
            
        return {
            "analysis_type": "intent_classification",
            "intent": intent,
            "confidence": 0.8
        }
    
    else:
        return {
            "analysis_type": "unknown",
            "error": f"Unsupported analysis type: {analysis_type}",
            "supported_types": ["topic", "sentiment", "intent"]
        }


@app.generic_trigger(
    arg_name="context",
    type="mcpToolTrigger",
    toolName="topic_analyzer_mcp",
    description="This function analyzes user queries to extract topics, sentiment, or intent using Azure OpenAI-like processing.",
    toolProperties=tool_properties_topic_analyzer_json,
)
def topic_analyzer_mcp(context) -> str:
    """
    A function to analyze user queries for topic extraction, sentiment analysis, or intent classification.
    
    This function simulates Azure OpenAI processing to analyze user input and return structured results.

    Args:
        context: The trigger context containing user query and analysis type.

    Returns:
        str: JSON string containing the analysis results.
    """
    try:
        content = json.loads(context)
        user_query = content["arguments"][_USER_QUERY_PROPERTY_NAME]
        analysis_type = content["arguments"].get(_ANALYSIS_TYPE_PROPERTY_NAME, "topic")

        logging.info("Topic Analyzer MCP function triggered.")
        logging.info(f"Context: {context}")
        logging.info(f"User query: {user_query}")
        logging.info(f"Analysis type: {analysis_type}")

        # Call the pseudo Azure OpenAI function
        analysis_result = pseudo_azure_openai_call(user_query, analysis_type)
        
        # Add metadata to the result
        analysis_result.update({
            "original_query": user_query,
            "timestamp": "2024-01-01T00:00:00Z",  # In real implementation, use actual timestamp
            "model": "azure-openai-gpt-4",  # Simulated model name
            "processing_time_ms": 150  # Simulated processing time
        })
        
        logging.info(f"Analysis result: {analysis_result}")
        
        return json.dumps(analysis_result, indent=2)
        
    except KeyError as e:
        error_msg = f"Missing required parameter: {str(e)}"
        logging.error(error_msg)
        return json.dumps({"error": error_msg, "status": "failed"})
    
    except json.JSONDecodeError as e:
        error_msg = f"Invalid JSON in context: {str(e)}"
        logging.error(error_msg)
        return json.dumps({"error": error_msg, "status": "failed"})
    
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logging.error(error_msg)
        return json.dumps({"error": error_msg, "status": "failed"})


# Additional helper function for batch processing (optional)
@app.generic_trigger(
    arg_name="context",
    type="mcpToolTrigger", 
    toolName="batch_topic_analyzer_mcp",
    description="Batch process multiple user queries for topic analysis.",
    toolProperties=json.dumps([
        ToolProperty("queries", "array", "Array of user queries to analyze.").to_dict(),
        ToolProperty("analysis_type", "string", "Type of analysis: 'topic', 'sentiment', or 'intent'.").to_dict()
    ]),
)
def batch_topic_analyzer_mcp(context) -> str:
    """
    Batch process multiple user queries for analysis.
    
    Args:
        context: The trigger context containing array of queries and analysis type.
        
    Returns:
        str: JSON string containing batch analysis results.
    """
    try:
        content = json.loads(context)
        queries = content["arguments"]["queries"]
        analysis_type = content["arguments"].get("analysis_type", "topic")
        
        logging.info(f"Batch Topic Analyzer triggered with {len(queries)} queries")
        
        results = []
        for i, query in enumerate(queries):
            try:
                analysis_result = pseudo_azure_openai_call(query, analysis_type)
                analysis_result.update({
                    "query_index": i,
                    "original_query": query
                })
                results.append(analysis_result)
            except Exception as e:
                results.append({
                    "query_index": i,
                    "original_query": query,
                    "error": str(e),
                    "status": "failed"
                })
        
        batch_result = {
            "batch_id": f"batch_{len(queries)}_queries",
            "total_queries": len(queries),
            "successful_analyses": len([r for r in results if "error" not in r]),
            "failed_analyses": len([r for r in results if "error" in r]),
            "results": results
        }
        
        return json.dumps(batch_result, indent=2)
        
    except Exception as e:
        error_msg = f"Batch processing error: {str(e)}"
        logging.error(error_msg)
        return json.dumps({"error": error_msg, "status": "failed"})
