swagger: '2.0'
info:
  version: '1.0.0'
  title: Test Weather MCP Server
  description: OpenAI API Documentation
host: api.openai.com
basePath: /
schemes:
  - https
consumes: []
produces: []
paths: 
  /runtime/webhooks/mcp/sse?code=JC6idvkgLZ6tRrGhVQdiGkvFoG9pkFzLAu-HYb6yPwLqAzFu_r-4Fg==:
    post:
      summary: Test Weather MCP Server
      x-ms-agentic-protocol: mcp-streamable-1.0
      operationId: InvokeMCP
      responses:
        '200':
          description: Successful
        '401':
          description: Unauthorized - Invalid API key.
        '500':
          description: Internal Server Error - An error occurred on the server.
definitions: {}
securityDefinitions: {}
security: []
tags: []